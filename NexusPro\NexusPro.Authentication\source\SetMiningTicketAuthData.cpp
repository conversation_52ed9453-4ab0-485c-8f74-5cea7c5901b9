/*
 * SetMiningTicketAuthData.cpp
 * Original Function: MiningTicket::_AuthKeyTicket::Set (overload)
 * Original Address: 0x140078ED0
 * 
 * Description: Sets the authentication key ticket data directly from a source value.
 * This is a simple overload that copies the entire 32-bit data field at once.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Set mining ticket authentication key data directly
void MiningTicket::_AuthKeyTicket::Set(MiningTicket::_AuthKeyTicket *this, unsigned int uiSrc) {
    // Copy the entire data field directly
    this->uiData = uiSrc;
}
