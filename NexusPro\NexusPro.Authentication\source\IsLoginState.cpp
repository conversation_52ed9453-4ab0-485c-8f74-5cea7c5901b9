/*
 * IsLoginState.cpp
 * Original Function: ?IsLogInState@CUnmannedTraderUserInfo@@_NXZ
 * Original Address: 0x140366F20
 * 
 * Description: Checks if the unmanned trader user is in a logged-in state.
 * Returns true if the user state equals 1 (logged in), false otherwise.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Check if unmanned trader user is in logged-in state
bool CUnmannedTraderUserInfo::IsLogInState(CUnmannedTraderUserInfo *this) {
    int *v1;                           // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    int v4;                           // Stack buffer [sp+0h] [bp-18h]
    CUnmannedTraderUserInfo *v5;      // This pointer [sp+20h] [bp+8h]

    v5 = this;
    v1 = &v4;
    
    // Initialize stack buffer with debug pattern
    for (i = 4LL; i; --i) {
        *v1 = 0xCCCCCCCC;
        ++v1;
    }
    
    // Return true if user state is 1 (logged in)
    return v5->m_eState == 1;
}
