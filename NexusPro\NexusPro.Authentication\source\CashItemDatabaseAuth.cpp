/*
 * CashItemDatabaseAuth.cpp
 * Original Function: CRFCashItemDatabase::CallProc_RFOnlineAuth
 * Original Address: 0x140482430
 * 
 * Description: Handles authentication for cash item database operations.
 * Executes stored procedure to authenticate and retrieve cash amount for user accounts.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;

#include <cstring>

// Handle cash item database authentication
signed __int64 CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemDatabase *this, _param_cash_select *rParam) {
    __int64 *v2;                      // Stack pointer for initialization
    signed __int64 i;                 // Loop counter for stack initialization
    signed __int64 result;            // Return value
    __int64 v5;                       // Stack buffer [sp+0h] [bp-188h]
    void *SQLStmt;                    // SQL statement handle [sp+20h] [bp-168h]
    SQLLEN *StrLen_or_IndPtr;         // String length indicator [sp+28h] [bp-160h]
    char DstBuf;                      // SQL query buffer [sp+40h] [bp-148h]
    char v9;                          // Buffer continuation [sp+41h] [bp-147h]
    SQLLEN v10;                       // SQL length value [sp+158h] [bp-30h]
    __int16 v11;                      // SQL return code [sp+164h] [bp-24h]
    unsigned __int8 v12;              // Error code [sp+168h] [bp-20h]
    unsigned __int8 v13;              // Error code [sp+169h] [bp-1Fh]
    unsigned __int64 v14;             // Security cookie [sp+178h] [bp-10h]
    CRFCashItemDatabase *v15;         // This pointer [sp+190h] [bp+8h]
    _param_cash_select *v16;          // Parameters [sp+198h] [bp+10h]

    v16 = rParam;
    v15 = this;
    v2 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 96LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;   // Debug fill pattern
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    // Set up security cookie
    v14 = (unsigned __int64)&v5 ^ _security_cookie;
    
    // Initialize SQL query buffer
    DstBuf = 0;
    memset(&v9, 0, 0xFF);
    
    // Format SQL stored procedure call
    sprintf_s(
        &DstBuf,
        0x100,
        "declare @out_amount int exec prc_rfonline_auth '%s', @s_amount = @out_amount output select @out_amount",
        rParam->in_szAcc);
    
    // Log query if debug logging is enabled
    if (v15->m_bSaveDBLog) {
        CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &DstBuf);
    }
    
    // Check database connection and reconnect if needed
    if (v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr)) {
        // Execute SQL query
        v11 = SQLExecDirect(v15->m_hStmtSelect, (SQLCHAR *)&DstBuf, SQL_NTS);
        
        if (v11 && v11 != SQL_SUCCESS_WITH_INFO) {
            if (v11 == SQL_NO_DATA) {
                result = 2LL;  // No data found
            } else {
                // Handle SQL execution error
                SQLStmt = v15->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                result = 1LL;  // Error
            }
        } else {
            // Fetch result data
            v11 = SQLFetch(v15->m_hStmtSelect);
            
            if (v11 && v11 != SQL_SUCCESS_WITH_INFO) {
                v12 = 0;
                if (v11 == SQL_NO_DATA) {
                    v12 = 2;  // No data
                } else {
                    // Handle fetch error
                    SQLStmt = v15->m_hStmtSelect;
                    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
                    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                    v12 = 1;  // Error
                }
                
                // Close cursor
                if (v15->m_hStmtSelect) {
                    SQLCloseCursor(v15->m_hStmtSelect);
                }
                result = v12;
            } else {
                // Get cash amount data
                StrLen_or_IndPtr = &v10;
                SQLStmt = nullptr;
                v11 = SQLGetData(v15->m_hStmtSelect, 1, SQL_C_LONG, &v16->out_dwCashAmount, 0, &v10);
                
                if (v11 && v11 != SQL_SUCCESS_WITH_INFO) {
                    v13 = 0;
                    if (v11 == SQL_NO_DATA) {
                        v13 = 2;  // No data
                    } else {
                        // Handle get data error
                        SQLStmt = v15->m_hStmtSelect;
                        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                        v13 = 1;  // Error
                    }
                    
                    // Close cursor
                    if (v15->m_hStmtSelect) {
                        SQLCloseCursor(v15->m_hStmtSelect);
                    }
                    result = v13;
                } else {
                    // Success - close cursor and log
                    if (v15->m_hStmtSelect) {
                        SQLCloseCursor(v15->m_hStmtSelect);
                    }
                    
                    if (v15->m_bSaveDBLog) {
                        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &DstBuf);
                    }
                    result = 0LL;  // Success
                }
            }
        }
    } else {
        // Database connection failed
        CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
        result = 1LL;  // Error
    }
    
    return result;
}
