/*
 * AutoTradeLoginSell.cpp
 * Original Function: ?auto_trade_login_sell@CMgrAvatorItemHistory@@XPEBDK0KPEAU_db_con@_STORAGE_LIST@@_JKKKKPEAD@Z
 * Original Address: 0x14023A3E0
 * 
 * Description: Logs auto trade sell transactions when a player logs in.
 * This function records detailed information about completed auto trade sales.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Log auto trade sell transaction on login
void CMgrAvatorItemHistory::auto_trade_login_sell(
    CMgrAvatorItemHistory *this, 
    const char *szBuyerName, 
    unsigned int dwBuyerSerial, 
    const char *szBuyerID, 
    unsigned int dwRegistSerial, 
    _STORAGE_LIST::_db_con *pItem, 
    __int64 tResultTime, 
    unsigned int dwPrice, 
    unsigned int dwTax, 
    unsigned int dwLeftDalant, 
    unsigned int dwLeftGold, 
    char *pszFileName) {
    
    __int64 *v12;                      // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    int v14;                          // Month value
    int v15;                          // Year value
    char *v16;                        // Item upgrade info string
    __int64 v17;                      // Stack buffer [sp+0h] [bp-B8h]
    unsigned __int64 v18;             // Year parameter [sp+20h] [bp-98h]
    char *v19;                        // Day parameter [sp+28h] [bp-90h]
    unsigned __int64 v20;             // Hour parameter [sp+30h] [bp-88h]
    unsigned int v21;                 // Minute parameter [sp+38h] [bp-80h]
    unsigned int v22;                 // Second parameter [sp+40h] [bp-78h]
    unsigned int v23;                 // Registration serial [sp+48h] [bp-70h]
    const char *v24;                  // Buyer name [sp+50h] [bp-68h]
    char *v25;                        // Buyer serial [sp+58h] [bp-60h]
    void *v26;                        // Buyer ID [sp+60h] [bp-58h]
    unsigned int v27;                 // Price [sp+68h] [bp-50h]
    unsigned int v28;                 // Tax [sp+70h] [bp-48h]
    unsigned int v29;                 // Left dalant [sp+78h] [bp-40h]
    unsigned int v30;                 // Left gold [sp+80h] [bp-38h]
    char *v31;                        // Current date [sp+88h] [bp-30h]
    char *v32;                        // Current time [sp+90h] [bp-28h]
    tm *v33;                          // Time structure [sp+A0h] [bp-18h]
    _base_fld *v34;                   // Item record data [sp+A8h] [bp-10h]
    CMgrAvatorItemHistory *v35;       // This pointer [sp+C0h] [bp+8h]
    const char *v36;                  // Buyer name copy [sp+C8h] [bp+10h]
    unsigned int v37;                 // Buyer serial copy [sp+D0h] [bp+18h]
    const char *v38;                  // Buyer ID copy [sp+D8h] [bp+20h]

    v38 = szBuyerID;
    v37 = dwBuyerSerial;
    v36 = szBuyerName;
    v35 = this;
    v12 = &v17;
    
    // Initialize stack buffer with debug pattern
    for (i = 44LL; i; --i) {
        *(DWORD *)v12 = 0xCCCCCCCC;
        v12 = (__int64 *)((char *)v12 + 4);
    }
    
    // Get local time from result time
    v33 = localtime(&tResultTime);
    sBuf[0] = 0;  // Clear buffer
    
    // Format log message with timestamp
    if (v33) {
        v14 = v33->tm_mon + 1;  // Month (1-12)
        v15 = v33->tm_year;     // Year since 1900
        v32 = v35->m_szCurTime;
        v31 = v35->m_szCurDate;
        v30 = dwLeftGold;
        v29 = dwLeftDalant;
        v28 = dwTax;
        v27 = dwPrice;
        v26 = (void *)v38;
        v25 = (char *)v37;
        v24 = v36;
        v23 = dwRegistSerial;
        v22 = v33->tm_sec;
        v21 = v33->tm_min;
        v20 = v33->tm_hour;
        v19 = (char *)v33->tm_mday;
        v18 = v14;
        
        sprintf_s(
            sBuf,
            0x2800,
            "AUTO TRADE(SELL) : login sell selldate(%04d-%02d-%02d %02d:%02d:%02d) reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u)"
            " $D:%u $G:%u [%s %s]\r\n",
            (unsigned int)(v15 + 1900),  // Full year
            v18,                         // Month
            v19,                         // Day
            v20,                         // Hour
            v21,                         // Minute
            v22,                         // Second
            v23,                         // Registration serial
            v24,                         // Buyer name
            v25,                         // Buyer serial
            v26,                         // Buyer ID
            v27,                         // Price
            v28,                         // Tax
            v29,                         // Left dalant
            v30,                         // Left gold
            v31,                         // Current date
            v32);                        // Current time
    } else {
        // Handle invalid timestamp
        v26 = (void *)v35->m_szCurTime;
        v25 = v35->m_szCurDate;
        v24 = (const char *)dwLeftGold;
        v23 = dwLeftDalant;
        v22 = dwTax;
        v21 = dwPrice;
        v20 = (unsigned __int64)v38;
        v19 = (char *)v37;
        v18 = (unsigned __int64)v36;
        
        sprintf_s(
            sBuf,
            0x2800,
            "AUTO TRADE(SELL) : login sell selldate(invalid) reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
            dwRegistSerial,
            v18,                         // Buyer name
            v19,                         // Buyer serial
            v20,                         // Buyer ID
            v21,                         // Price
            v22,                         // Tax
            v23,                         // Left dalant
            v24,                         // Left gold
            v25,                         // Current date
            v26);                        // Current time
    }
    
    // Append to main data buffer
    strcat_s(sData, 0x4E20, sBuf);
    
    // Get item record data and format item information
    v34 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
    v16 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
    v20 = pItem->m_lnUID;
    v19 = v16;
    v18 = pItem->m_dwDur;
    
    // Format item details
    sprintf_s(sBuf, 0x2800, "\t- %s_%u_@%s[%I64u]\r\n", 
        v34->m_strCode,  // Item code
        v18,             // Item durability
        v19,             // Upgrade info
        v20);            // Item UID
    
    // Append item details to main data buffer
    strcat_s(sData, 0x4E20, sBuf);
}
