/*
 * NationConnectSession.cpp
 * Original Function: CNationSettingManager::OnConnectSession
 * Original Address: 0x140229400
 * 
 * Description: Handles session connection for nation-specific game guard system.
 * This function initializes the game guard system when a new session connects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Handle session connection for nation setting manager
void CNationSettingManager::OnConnectSession(CNationSettingManager *this, int n) {
    __int64 *v2;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v4;                       // Stack buffer [sp+0h] [bp-38h]
    INationGameGuardSystem *v5;       // Game guard system interface [sp+20h] [bp-18h]
    CNationSettingManager *v6;        // This pointer [sp+40h] [bp+8h]
    int v7;                           // Session index [sp+48h] [bp+10h]

    v7 = n;
    v6 = this;
    v2 = &v4;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;   // Debug fill pattern
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    // Check if game guard system is available
    if (CNationSettingData::GetGameGuardSystem(v6->m_pData)) {
        // Get the game guard system interface
        v5 = CNationSettingData::GetGameGuardSystem(v6->m_pData);
        
        // Call the OnConnectSession method through virtual function table
        (*(void (**)(INationGameGuardSystem *, QWORD))&v5->vfptr->gap8[0])(v5, (unsigned int)v7);
    }
}
