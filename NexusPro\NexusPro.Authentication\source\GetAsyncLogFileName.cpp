/**
 * @file GetAsyncLogFileName.cpp
 * @brief RF Online Async Log Info Get File Name Function
 * @note Original Function: ?GetFileName@CAsyncLogInfo@@PEBDXZ
 * @note Original Address: 0x1403C16D0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Gets the file name for the async log
 * @param this Pointer to CAsyncLogInfo instance
 * @return char* Pointer to the log file name string
 * 
 * This function returns the complete file name (including path) where
 * log entries are written. The file name is set during initialization
 * and may include date/time stamps depending on configuration.
 */
char *CAsyncLogInfo::GetFileName(CAsyncLogInfo *this) {
  return this->m_szLogFileName;
}
