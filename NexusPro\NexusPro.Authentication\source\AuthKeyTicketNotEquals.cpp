/*
 * AuthKeyTicketNotEquals.cpp
 * Original Function: MiningTicket::_AuthKeyTicket::operator!=
 * Original Address: 0x1400CFE90
 * 
 * Description: Inequality comparison operator for mining ticket authentication keys.
 * Compares two authentication key tickets to determine if they are not equal.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Inequality comparison operator for mining ticket authentication keys
bool MiningTicket::_AuthKeyTicket::operator!=(MiningTicket::_AuthKeyTicket *this, MiningTicket::_AuthKeyTicket *Src) {
    int *v2;                          // Stack pointer for initialization
    signed __int64 i;                 // Loop counter for stack initialization
    int v5;                           // Stack buffer [sp+0h] [bp-18h]
    MiningTicket::_AuthKeyTicket *v6; // This pointer [sp+20h] [bp+8h]

    v6 = this;
    v2 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 4LL; i; --i) {
        *v2 = 0xCCCCCCCC;            // Debug fill pattern
        ++v2;
    }
    
    // Compare the authentication key data for inequality
    return v6->uiData != Src->uiData;
}
