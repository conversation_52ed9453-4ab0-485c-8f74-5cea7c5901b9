/*
 * UpdateAsyncLogFileName.cpp
 * Original Function: ?UpdateLogFileName@CAsyncLogInfo@@XXZ
 * Original Address: 0x1403BD0F0
 * 
 * Description: Updates the log file name for the CAsyncLogInfo object.
 * This function generates a new log file name based on current date/time
 * and updates the internal file name member variable.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;

/**
 * Updates the log file name with current date/time stamp
 * @param this Pointer to the CAsyncLogInfo instance
 * @param a2 Additional parameter for stack allocation
 */
void __usercall CAsyncLogInfo::UpdateLogFileName(CAsyncLogInfo *this@<rcx>, signed __int64 a2@<rax>) {
    void *v2; // rsp@1
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp-20h] [bp-2998h]@1
    char *v6; // [sp+0h] [bp-2978h]@7
    char szTime[256]; // [sp+20h] [bp-2958h]@6 - Time string buffer
    char DstBuf[10240]; // [sp+140h] [bp-2838h]@7 - Destination buffer for file name
    rsize_t SizeInBytes; // [sp+2948h] [bp-30h]@7
    int v12; // [sp+2950h] [bp-28h]@7
    unsigned __int64 v13; // [sp+2960h] [bp-18h]@4
    CAsyncLogInfo *v14; // [sp+2980h] [bp+8h]@1

    v14 = this;
    v2 = alloca(a2);
    v3 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 2660LL; i; --i) {
        *(DWORD *)v3 = 0xCCCCCCCC;
        v3 = (__int64 *)((char *)v3 + 4);
    }
    
    // Set up security cookie for stack protection
    v13 = (unsigned __int64)&v5 ^ _security_cookie;
    
    // Check if timer is available and counting
    if (v14->m_pkTimer) {
        if (CMyTimer::CountingTimer(v14->m_pkTimer)) {
            // Initialize time string buffer
            memset(szTime, 0, sizeof(szTime));
            
            // Get current date/time string
            if (GetDateTimeStr(szTime)) {
                // Initialize destination buffer
                memset(DstBuf, 0, sizeof(DstBuf));
                
                // Calculate required buffer size
                SizeInBytes = strlen(v14->m_szLogFileName) + 1;
                v6 = szTime;
                
                // Format the new log file name with timestamp
                v12 = sprintf_s(DstBuf, sizeof(DstBuf), "%s_%s.log", v14->m_szLogDirPath, v6);
                
                if (v12 > 0) {
                    // Thread-safe update of log file name
                    CNetCriticalSection::Lock(&v14->m_csLock);
                    strcpy_s(v14->m_szLogFileName, SizeInBytes, DstBuf);
                    CNetCriticalSection::Unlock(&v14->m_csLock);
                }
            }
        }
    }
}
