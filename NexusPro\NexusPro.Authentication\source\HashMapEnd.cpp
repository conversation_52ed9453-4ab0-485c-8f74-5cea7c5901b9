/*
 * HashMapEnd.cpp
 * Original Function: Hash map end iterator
 * Original Address: 0x1403C1990
 * 
 * Description: Returns an iterator to the end of the hash map container.
 * Provides access to the past-the-end element in the AsyncLogInfo hash map.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Get end iterator for AsyncLogInfo hash map
std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator 
stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>>::end(
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>> *this,
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator *result) {
    
    __int64 *v2;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v5;                       // Stack buffer [sp+0h] [bp-38h]
    int v6;                           // Iterator index [sp+20h] [bp-18h]
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>> *v7; // This pointer [sp+40h] [bp+8h]
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator *resulta; // Result pointer [sp+48h] [bp+10h]

    resulta = result;
    v7 = this;
    v2 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;   // Debug fill pattern
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    v6 = 0;  // Iterator index
    
    // Get end iterator from the underlying list
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::end(
        &v7->_List,
        result);
    
    return *resulta;
}
