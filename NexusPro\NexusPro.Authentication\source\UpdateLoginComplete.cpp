/*
 * UpdateLoginComplete.cpp
 * Original Function: ?UpdateLogInComplete@CUnmannedTraderController@@EPEAD@Z
 * Original Address: 0x14034E440
 * 
 * Description: Updates the login completion status for unmanned trader operations.
 * This function processes trader data and updates database records based on item states.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// Update login completion status for unmanned trader
char CUnmannedTraderController::UpdateLogInComplete(CUnmannedTraderController *this, char *pData) {
    __int64 *v2;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v5;                       // Stack buffer [sp+0h] [bp-88h]
    _SYSTEMTIME *kCurTime;            // Current time pointer [sp+20h] [bp-68h]
    unsigned int dwTax;               // Tax amount [sp+28h] [bp-60h]
    _SYSTEMTIME *v8;                  // System time pointer [sp+30h] [bp-58h]
    char *v9;                         // Data pointer [sp+40h] [bp-48h]
    char Dst;                         // System time buffer [sp+58h] [bp-30h]
    int j;                            // Loop counter [sp+74h] [bp-14h]
    int v12;                          // State value [sp+78h] [bp-10h]

    v2 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 32LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    v9 = pData;
    pData[8] = 1;  // Set success flag
    
    // Initialize system time buffer
    memset(&Dst, 0, 0x10);
    GetLocalTime((LPSYSTEMTIME)&Dst);
    
    // Process each trader item entry
    for (j = 0; j < *((WORD *)v9 + 5); ++j) {
        v9[16 * j + 12] = 0;  // Clear error flag
        v12 = (unsigned __int8)v9[16 * j + 13];  // Get item state
        v12 -= 37;  // Normalize state value
        
        switch (v12) {
            case 0:   // State 37: Item available
            case 45:  // State 82: Item sold
            case 46:  // State 83: Item expired
            case 53:  // State 90: Item cancelled
            case 54:  // State 91: Item returned
            case 57:  // State 94: Item completed
                kCurTime = (_SYSTEMTIME *)&Dst;
                
                // Update unmanned trader item state in database
                if (!CRFWorldDatabase::Update_UnmannedTraderItemState(
                        pkDB,
                        v9[9],                          // Trader type
                        *(DWORD *)&v9[16 * j + 20],    // Item serial
                        v9[16 * j + 24],               // Update state
                        (_SYSTEMTIME *)&Dst)) {        // Current time
                    v9[16 * j + 12] = 1;  // Set error flag
                    v9[8] = 0;            // Set failure flag
                }
                break;
                
            case 55:  // State 92: Result info update
                v8 = (_SYSTEMTIME *)&Dst;
                dwTax = 0;
                kCurTime = 0;
                
                // Update unmanned trader result information
                if (!CRFWorldDatabase::Update_UnmannedTraderResultInfo(
                        pkDB,
                        v9[9],                          // Trader type
                        *(DWORD *)&v9[16 * j + 20],    // Item serial
                        v9[16 * j + 24],               // Update state
                        0,                              // Tax amount
                        0,                              // Additional parameter
                        (_SYSTEMTIME *)&Dst)) {        // Current time
                    v9[16 * j + 12] = 1;  // Set error flag
                    v9[8] = 0;            // Set failure flag
                }
                break;
                
            default:
                continue;  // Skip unknown states
        }
    }
    
    return 0;  // Return success
}
