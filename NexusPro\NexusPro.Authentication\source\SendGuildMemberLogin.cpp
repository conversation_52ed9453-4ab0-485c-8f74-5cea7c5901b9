/*
 * SendGuildMemberLogin.cpp
 * Original Function: ?SendMsg_GuildMemberLogin@CGuild@@XKGG@Z
 * Original Address: 0x1402570F0
 * 
 * Description: Sends guild member login notification to all other guild members.
 * This function notifies all guild members when a member logs in, including location info.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Send guild member login notification to all guild members
void CGuild::SendMsg_GuildMemberLogin(CGuild *this, unsigned int dwSerial, unsigned __int16 wMapCode, unsigned __int16 wRegionIndex) {
    __int64 *v4;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v6;                       // Stack buffer [sp+0h] [bp-78h]
    char szMsg[4];                    // Message buffer [sp+34h] [bp-44h]
    unsigned __int16 v8;              // Map code [sp+38h] [bp-40h]
    char v9;                          // Region index [sp+3Ah] [bp-3Eh]
    char pbyType;                     // Message type [sp+54h] [bp-24h]
    char v11;                         // Message subtype [sp+55h] [bp-23h]
    int j;                            // Loop counter [sp+64h] [bp-14h]
    CGuild *v13;                      // This pointer [sp+80h] [bp+8h]
    unsigned int v14;                 // Member serial [sp+88h] [bp+10h]

    v14 = dwSerial;
    v13 = this;
    v4 = &v6;
    
    // Initialize stack buffer with debug pattern
    for (i = 28LL; i; --i) {
        *(DWORD *)v4 = 0xCCCCCCCC;
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    // Prepare login notification message
    *(DWORD *)szMsg = dwSerial;       // Member serial number
    v8 = wMapCode;                    // Current map code
    v9 = wRegionIndex;                // Current region index
    pbyType = 27;                     // Guild message type
    v11 = 44;                         // Login notification subtype
    
    // Send notification to all other guild members
    for (j = 0; j < 50; ++j) {
        // Check if member slot is filled and has an active player
        if (_guild_member_info::IsFill(&v13->m_MemberData[j])
            && v13->m_MemberData[j].pPlayer
            && v13->m_MemberData[j].dwSerial != v14) {  // Don't send to self
            
            // Send login notification message to guild member
            CNetProcess::LoadSendMsg(
                unk_1414F2088,                                      // Network handler
                v13->m_MemberData[j].pPlayer->m_ObjID.m_wIndex,    // Target player index
                &pbyType,                                           // Message type
                szMsg,                                              // Message data
                7u);                                                // Message size
        }
    }
}
