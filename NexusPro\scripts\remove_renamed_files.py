#!/usr/bin/env python3
"""
<PERSON>ript to remove original decompiled files that have been renamed to shorter, more readable names.
This script identifies files that have been renamed and removes the original long-named versions.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Set, <PERSON><PERSON>

def is_decompiled_filename(filename: str) -> bool:
    """Check if a filename appears to be a decompiled function name."""
    # Remove extension for analysis
    name_without_ext = filename.rsplit('.', 1)[0]
    
    # Patterns that indicate decompiled names
    decompiled_patterns = [
        r'^[0-9]',  # Starts with number
        r'[A-Z][a-z]+[A-Z][a-z]+.*_[0-9A-F]{8,}$',  # CamelCase with hex suffix
        r'^j_',  # Jump table functions
        r'QEAA|UEAA|MEAA|YEAA',  # MSVC mangled function signatures
        r'std[a-z_]+',  # STL template instantiations
        r'_[A-Z][a-z]+.*_[0-9A-F]{8,}$',  # Underscore prefix with hex
        r'[a-z]+[A-Z][a-z]+.*[0-9A-F]{8,}$',  # Mixed case with hex
        r'^dtor[0-9]+',  # Destructor functions
        r'allocator.*[0-9A-F]{8,}$',  # Allocator functions
        r'iterator.*[0-9A-F]{8,}$',  # Iterator functions
        r'vector.*[0-9A-F]{8,}$',  # Vector functions
        r'hash_map.*[0-9A-F]{8,}$',  # Hash map functions
        r'Validate.*[0-9A-F]{8,}$',  # Validation functions
        r'Generate.*[0-9A-F]{8,}$',  # Generation functions
    ]
    
    return any(re.search(pattern, name_without_ext) for pattern in decompiled_patterns)

def is_readable_filename(filename: str) -> bool:
    """Check if a filename appears to be a human-readable renamed file."""
    name_without_ext = filename.rsplit('.', 1)[0]
    
    # Should be relatively short and use readable naming conventions
    if len(name_without_ext) > 50:
        return False
    
    # Should not contain hex addresses or mangled signatures
    if re.search(r'[0-9A-F]{8,}|QEAA|UEAA|MEAA|YEAA', name_without_ext):
        return False
    
    # Should not start with numbers or j_
    if re.match(r'^[0-9]|^j_', name_without_ext):
        return False
    
    # Should use reasonable naming (letters, numbers, underscores)
    if not re.match(r'^[A-Za-z][A-Za-z0-9_]*$', name_without_ext):
        return False
    
    return True

def find_potential_renames(directory: Path) -> Dict[str, List[str]]:
    """Find potential renamed files by grouping similar function names."""
    files = []
    for ext in ['.cpp', '.h']:
        files.extend(directory.glob(f'*{ext}'))

    readable_files = []
    decompiled_files = []

    for file in files:
        if is_readable_filename(file.name):
            readable_files.append(file)
        elif is_decompiled_filename(file.name):
            decompiled_files.append(file)

    # Known mappings for Authentication module
    known_mappings = {
        'AccountServerLogin': ['AccountServerLoginCMainThreadQEAAXXZ', 'j_AccountServerLoginCMainThreadQEAAXXZ'],
        'AsyncLogInit': ['InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA', 'j_InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKA'],
        'AuthCriTicket': ['AuthLastCriTicketMiningTicketQEAAHGEEEEZ', 'j_AuthLastCriTicketMiningTicketQEAAHGEEEEZ'],
        'AuthMentalTicket': ['AuthLastMentalTicketMiningTicketQEAAHGEEEEZ', 'j_AuthLastMentalTicketMiningTicketQEAAHGEEEEZ'],
        'BillingLogin': ['LoginCBillingManagerQEAAXPEAVCUserDBZ', 'j_LoginCBillingManagerQEAAXPEAVCUserDBZ',
                        'LoginCBillingUEAAXPEAVCUserDBZ', 'j_LoginCBillingUEAAXPEAVCUserDBZ',
                        'LoginCBillingIDUEAAXPEAVCUserDBZ', 'j_LoginCBillingIDUEAAXPEAVCUserDBZ',
                        'LoginCBillingJPUEAAXPEAVCUserDBZ', 'j_LoginCBillingJPUEAAXPEAVCUserDBZ',
                        'LoginCBillingNULLUEAAXPEAVCUserDBZ', 'j_LoginCBillingNULLUEAAXPEAVCUserDBZ'],
        'GetAsyncLogCount': ['GetCountCAsyncLogInfoQEAAKXZ', 'j_GetCountCAsyncLogInfoQEAAKXZ'],
        'GetAsyncLogDirPath': ['GetDirPathCAsyncLogInfoQEAAPEBDXZ', 'j_GetDirPathCAsyncLogInfoQEAAPEBDXZ'],
        'GetAsyncLogFileName': ['GetFileNameCAsyncLogInfoQEAAPEBDXZ', 'j_GetFileNameCAsyncLogInfoQEAAPEBDXZ'],
        'InitAuthKeyTicket': ['Init_AuthKeyTicketMiningTicketQEAAXXZ', 'j_Init_AuthKeyTicketMiningTicketQEAAXXZ'],
        'SetAuthKeyTicket': ['Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ', 'j_Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ',
                            'Set_AuthKeyTicketMiningTicketQEAAXIZ', 'j_Set_AuthKeyTicketMiningTicketQEAAXIZ'],
    }

    # Group by potential matches
    potential_renames = {}

    for readable_file in readable_files:
        readable_name = readable_file.stem
        potential_matches = []

        # Check known mappings first
        if readable_name in known_mappings:
            for pattern in known_mappings[readable_name]:
                for decompiled_file in decompiled_files:
                    if pattern in decompiled_file.stem:
                        potential_matches.append(decompiled_file)
        else:
            # Fallback to pattern matching
            for decompiled_file in decompiled_files:
                decompiled_name = decompiled_file.stem.lower()
                readable_lower = readable_name.lower()

                # Look for partial matches in the decompiled name
                if readable_lower in decompiled_name or any(
                    part in decompiled_name for part in readable_lower.split('_') if len(part) > 3
                ):
                    potential_matches.append(decompiled_file)

        if potential_matches:
            potential_renames[readable_file.name] = [f.name for f in potential_matches]

    return potential_renames

def scan_module(module_path: Path) -> Tuple[List[str], Dict[str, List[str]]]:
    """Scan a module directory for renamed files."""
    print(f"\nScanning module: {module_path.name}")
    
    headers_dir = module_path / "headers"
    source_dir = module_path / "source"
    
    files_to_remove = []
    potential_renames = {}
    
    for directory in [headers_dir, source_dir]:
        if directory.exists():
            print(f"  Checking {directory.name}/")
            renames = find_potential_renames(directory)
            if renames:
                potential_renames.update({f"{directory.name}/{k}": v for k, v in renames.items()})
                
                # Add the decompiled files to removal list
                for readable_file, decompiled_matches in renames.items():
                    for decompiled_file in decompiled_matches:
                        files_to_remove.append(str(directory / decompiled_file))
    
    return files_to_remove, potential_renames

def main():
    """Main function to scan all modules and remove renamed files."""
    script_dir = Path(__file__).parent
    nexus_dir = script_dir.parent
    
    if not nexus_dir.exists():
        print(f"Error: NexusPro directory not found at {nexus_dir}")
        return 1
    
    print("NexusPro Renamed Files Cleanup Tool")
    print("=" * 50)
    
    # Find all module directories
    module_dirs = [d for d in nexus_dir.iterdir() 
                   if d.is_dir() and d.name.startswith('NexusPro.') and d.name != 'NexusPro.Core']
    
    all_files_to_remove = []
    all_potential_renames = {}
    
    # Scan each module
    for module_dir in sorted(module_dirs):
        files_to_remove, potential_renames = scan_module(module_dir)
        all_files_to_remove.extend(files_to_remove)
        all_potential_renames.update(potential_renames)
    
    if not all_files_to_remove:
        print("\nNo renamed files detected. All files appear to be in their original state.")
        return 0
    
    print(f"\nFound {len(all_files_to_remove)} files that appear to be original versions of renamed files:")
    print("\nPotential renames detected:")
    for readable_file, decompiled_matches in all_potential_renames.items():
        print(f"  {readable_file} -> {len(decompiled_matches)} original file(s)")
        for match in decompiled_matches[:3]:  # Show first 3 matches
            print(f"    - {match}")
        if len(decompiled_matches) > 3:
            print(f"    ... and {len(decompiled_matches) - 3} more")
    
    # Ask for confirmation
    print(f"\nThis will remove {len(all_files_to_remove)} files.")
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    
    if response != 'y':
        print("Operation cancelled.")
        return 0
    
    # Remove the files
    removed_count = 0
    for file_path in all_files_to_remove:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                removed_count += 1
                print(f"Removed: {file_path}")
        except Exception as e:
            print(f"Error removing {file_path}: {e}")
    
    print(f"\nRemoval complete. Removed {removed_count} files.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
