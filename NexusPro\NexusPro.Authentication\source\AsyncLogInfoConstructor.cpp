/*
 * AsyncLogInfoConstructor.cpp
 * Original Function: CAsyncLogInfo Constructor
 * Original Address: 0x1403BC9F0
 * 
 * Description: Default constructor for CAsyncLogInfo class.
 * Initializes async logging information with default values and current system time.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Default constructor for CAsyncLogInfo class
CAsyncLogInfo::CAsyncLogInfo() {
    // Local variables for stack initialization
    __int64 *v1;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v3;                       // Stack buffer [sp+0h] [bp-28h]
    
    v1 = &v3;
    
    // Initialize stack buffer with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD *)v1 = 0xCCCCCCCC;   // Debug fill pattern
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Initialize member variables with default values
    m_dwLogType = static_cast<DWORD>(-1);  // Invalid log type initially
    m_dwLogID = 0;                         // No log ID assigned
    m_strLogMessage = "";                  // Empty log message
    GetSystemTime(&m_LogTime);             // Set current system time
}
