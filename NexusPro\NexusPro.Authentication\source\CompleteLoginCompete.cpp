/*
 * CompleteLoginCompete.cpp
 * Original Function: ?CompleteLogInCompete@CUnmannedTraderController@@XPEAD@Z
 * Original Address: 0x14034EF80
 * 
 * Description: Completes the login competition process for unmanned trader controller.
 * This function processes login completion data and logs the results for trading operations.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Complete login competition process for unmanned trader
void CUnmannedTraderController::CompleteLogInCompete(CUnmannedTraderController *this, char *pData) {
    __int64 *v2;                        // Stack pointer for initialization
    signed __int64 i;                   // Loop counter for stack initialization
    unsigned int v4;                    // Word index from data
    unsigned int v5;                    // Type value from data
    int v6;                            // Process update value
    __int64 v7;                        // Stack buffer [sp+0h] [bp-58h]
    int v8;                            // Seller ID [sp+20h] [bp-38h]
    int v9;                            // Update state [sp+28h] [bp-30h]
    int v10;                           // Registration state [sp+30h] [bp-28h]
    char *v11;                         // Data pointer [sp+40h] [bp-18h]
    unsigned int j;                    // Loop counter [sp+48h] [bp-10h]
    CUnmannedTraderController *v13;    // This pointer [sp+60h] [bp+8h]

    v13 = this;
    v2 = &v7;
    
    // Initialize stack buffer with debug pattern
    for (i = 20LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    v11 = pData;
    
    // Check if data is valid (byte 8 should not be null)
    if (!pData[8]) {
        v4 = *(WORD *)v11;              // Get word index
        v5 = (unsigned __int8)v11[9];   // Get type value
        v8 = *((DWORD *)v11 + 1);       // Get seller ID
        
        // Log the completion information
        CUnmannedTraderController::Log(
            v13,
            "CUnmannedTraderController::CompleteLogInCompete( BYTE byRet, char *pLoadData )\r\n"
            "\t\tType(%u) wInx(%u) dwSeller(%u)\r\n",
            v5,
            v4,
            v8);
        
        // Process each registration entry
        for (j = 0; (signed int)j < *((WORD *)v11 + 5); ++j) {
            // Check if registration is valid (not 255)
            if ((unsigned __int8)v11[16 * j + 13] != 255) {
                // Check if there's an error in the registration
                if (v11[16 * j + 12]) {
                    v6 = (unsigned __int8)v11[16 * j + 24];     // Process update value
                    v10 = (unsigned __int8)v11[16 * j + 13];    // Registration state
                    v9 = v6;                                     // Update state
                    v8 = *(DWORD *)&v11[16 * j + 16];          // Buyer ID
                    
                    // Log database error for this registration
                    CUnmannedTraderController::Log(
                        v13,
                        "\t\t(%d)Nth Regist Serial(%u) dwBuyer(%u) UpdateState(%u) byProcUpdate(%u) DB Error!\r\n",
                        j,
                        *(DWORD *)&v11[16 * j + 20],  // Registration serial
                        v8,                            // Buyer ID
                        v9,                            // Update state
                        v10);                          // Process update
                }
            }
        }
    }
}
