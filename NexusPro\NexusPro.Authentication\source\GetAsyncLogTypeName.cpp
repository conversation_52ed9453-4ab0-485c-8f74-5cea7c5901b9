/*
 * GetAsyncLogTypeName.cpp
 * Original Function: ?GetTypeName@CAsyncLogInfo@@PEBDXZ
 * Original Address: 0x1403C1650
 * 
 * Description: Returns the type name string for the CAsyncLogInfo object.
 * This function provides access to the internal type name member variable.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Gets the type name of the async log info object
 * @param this Pointer to the CAsyncLogInfo instance
 * @return Pointer to the type name string
 */
char *CAsyncLogInfo::GetTypeName(CAsyncLogInfo *this) {
    return this->m_szTypeName;
}
