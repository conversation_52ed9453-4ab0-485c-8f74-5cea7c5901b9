/*
 * LoginCancelAutoTrade.cpp
 * Original Function: CMgrAvatorItemHistory::login_cancel_auto_trade
 * Original Address: 0x140239D60
 * 
 * Description: Logs auto trade cancellation events when a player logs in.
 * This function records timeout-based auto trade cancellations with detailed item information.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Log auto trade cancellation on login
void CMgrAvatorItemHistory::login_cancel_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, __int64 tResultTime, char *pszFileName) {
    __int64 *v6;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    char *v8;                         // Item upgrade info (invalid time case)
    char *v9;                         // Item upgrade info (valid time case)
    int v10;                          // Month value
    int v11;                          // Year value
    __int64 v12;                      // Stack buffer [sp+0h] [bp-C8h]
    unsigned int v13;                 // Month parameter [sp+20h] [bp-A8h]
    char *v14;                        // Day parameter [sp+28h] [bp-A0h]
    unsigned __int64 v15;             // Hour parameter [sp+30h] [bp-98h]
    char *v16;                        // Minute parameter [sp+38h] [bp-90h]
    unsigned __int64 v17;             // Second parameter [sp+40h] [bp-88h]
    char *v18;                        // Registration serial [sp+48h] [bp-80h]
    char *v19;                        // Item code [sp+50h] [bp-78h]
    unsigned __int64 v20;             // Item durability [sp+58h] [bp-70h]
    char *v21;                        // Upgrade info [sp+60h] [bp-68h]
    unsigned __int64 v22;             // Item UID [sp+68h] [bp-60h]
    char *v23;                        // Current date [sp+70h] [bp-58h]
    char *v24;                        // Current time [sp+78h] [bp-50h]
    _base_fld *v25;                   // Item record data [sp+80h] [bp-48h]
    tm *v26;                          // Time structure [sp+88h] [bp-40h]
    char *v27;                        // Current time (invalid case) [sp+90h] [bp-38h]
    char *v28;                        // Current date (invalid case) [sp+98h] [bp-30h]
    int nTableCode;                   // Table code (invalid case) [sp+A0h] [bp-28h]
    char *v30;                        // Current time pointer [sp+A8h] [bp-20h]
    char *v31;                        // Current date pointer [sp+B0h] [bp-18h]
    int v32;                          // Table code [sp+B8h] [bp-10h]
    CMgrAvatorItemHistory *v33;       // This pointer [sp+D0h] [bp+8h]
    unsigned int v34;                 // Registration serial [sp+E0h] [bp+18h]
    _STORAGE_LIST::_db_con *v35;      // Item pointer [sp+E8h] [bp+20h]

    v35 = pRegItem;
    v34 = dwRegistSerial;
    v33 = this;
    v6 = &v12;
    
    // Initialize stack buffer with debug pattern
    for (i = 48LL; i; --i) {
        *(DWORD *)v6 = 0xCCCCCCCC;   // Debug fill pattern
        v6 = (__int64 *)((char *)v6 + 4);
    }
    
    // Get item record data
    v25 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pRegItem->m_byTableCode, pRegItem->m_wItemIndex);
    sBuf[0] = 0;  // Clear buffer
    
    // Get local time from result time
    v26 = localtime(&tResultTime);
    
    // Format log message with timestamp
    if (v26) {
        v30 = v33->m_szCurTime;
        v31 = v33->m_szCurDate;
        v32 = v35->m_byTableCode;
        v9 = DisplayItemUpgInfo(v32, v35->m_dwLv);
        v10 = v26->tm_mon + 1;  // Month (1-12)
        v11 = v26->tm_year;     // Year since 1900
        v24 = v30;
        v23 = v31;
        v22 = v35->m_lnUID;
        v21 = v9;
        v20 = v35->m_dwDur;
        v19 = v25->m_strCode;
        v18 = (char *)v34;
        v17 = v26->tm_sec;
        v16 = (char *)v26->tm_min;
        v15 = v26->tm_hour;
        v14 = (char *)v26->tm_mday;
        v13 = v10;
        
        sprintf_s(
            sBuf,
            0x2800,
            "TIMEOUT_AUTO_TRADE: login canceldate(%04d-%02d-%02d %02d:%02d:%02d) reg(%u) %s_%u_@%s[%I64u] [%s %s]\r\n",
            (unsigned int)(v11 + 1900),  // Full year
            v13,                         // Month
            v14,                         // Day
            v15,                         // Hour
            v16,                         // Minute
            v17,                         // Second
            v18,                         // Registration serial
            v19,                         // Item code
            v20,                         // Item durability
            v21,                         // Upgrade info
            v22,                         // Item UID
            v23,                         // Current date
            v24);                        // Current time
    } else {
        // Handle invalid timestamp
        v27 = v33->m_szCurTime;
        v28 = v33->m_szCurDate;
        nTableCode = v35->m_byTableCode;
        v8 = DisplayItemUpgInfo(nTableCode, v35->m_dwLv);
        v19 = v27;
        v18 = v28;
        v17 = v35->m_lnUID;
        v16 = v8;
        v15 = v35->m_dwDur;
        v14 = v25->m_strCode;
        v13 = v34;
        
        sprintf_s(
            sBuf,
            0x2800,
            "TIMEOUT_AUTO_TRADE: login canceldate(invalid(%u)) reg(%u) %s_%u_@%s[%I64u] [%s %s]\r\n",
            tResultTime,
            v13,                         // Registration serial
            v14,                         // Item code
            v15,                         // Item durability
            v16,                         // Upgrade info
            v17,                         // Item UID
            v18,                         // Current date
            v19);                        // Current time
    }
    
    // Append to main data buffer
    strcat_s(sData, 0x4E20, sBuf);
}
