/*
 * AsyncLogListBegin.cpp
 * Original Function: std::list begin iterator for AsyncLogInfo
 * Original Address: 0x1403C3670
 * 
 * Description: Returns an iterator to the beginning of the AsyncLogInfo list.
 * Provides access to the first element in the std::list container.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Get begin iterator for AsyncLogInfo list
std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator 
std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::begin(
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *this,
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator *result) {
    
    __int64 *v2;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    std::_List_nod<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::_Node **v4; // Next node pointer
    __int64 v6;                       // Stack buffer [sp+0h] [bp-38h]
    int v7;                           // Iterator index [sp+20h] [bp-18h]
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *v8; // This pointer [sp+40h] [bp+8h]
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator *v9; // Result pointer [sp+48h] [bp+10h]

    v9 = result;
    v8 = this;
    v2 = &v6;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;   // Debug fill pattern
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    v7 = 0;  // Start at beginning index
    
    // Get the next node from the head (first actual element)
    v4 = std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::_Nextnode(
        (std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *)v8->_Myhead,
        (std::_List_nod<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::_Node *)result);
    
    // Initialize the iterator with the first node
    std::list<std::pair<int const, CAsyncLogInfo *>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::iterator::iterator(
        v9,
        *v4);
    
    return *v9;
}
