/*
 * GuildBattleManagerLogin.cpp
 * Original Function: GUILD_BATTLE::CNormalGuildBattleManager::LogIn
 * Original Address: 0x1403D4360
 * 
 * Description: Handles guild member login for guild battle system.
 * This function manages player login during guild battles and determines appropriate actions.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Handle guild battle manager login
void GUILD_BATTLE::CNormalGuildBattleManager::LogIn(GUILD_BATTLE::CNormalGuildBattleManager *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial) {
    __int64 *v4;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v6;                       // Stack buffer [sp+0h] [bp-38h]
    GUILD_BATTLE::CNormalGuildBattle *v7; // Battle instance [sp+20h] [bp-18h]
    GUILD_BATTLE::CNormalGuildBattleManager *v8; // This pointer [sp+40h] [bp+8h]
    int na;                           // Player index [sp+48h] [bp+10h]
    unsigned int dwGuildSeriala;      // Guild serial [sp+50h] [bp+18h]
    unsigned int dwCharacSeriala;     // Character serial [sp+58h] [bp+20h]

    dwCharacSeriala = dwCharacSerial;
    dwGuildSeriala = dwGuildSerial;
    na = n;
    v8 = this;
    v4 = &v6;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v4 = 0xCCCCCCCC;   // Debug fill pattern
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    // Check if guild serial is valid (not -1)
    if (dwGuildSerial != -1) {
        v7 = nullptr;
        
        // Find the battle instance for this guild
        v7 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v8, dwGuildSerial);
        
        if (v7) {
            // Check if battle is in ready or count state
            if (GUILD_BATTLE::CNormalGuildBattle::IsReadyOrCountState(v7)) {
                // Battle is preparing, ask to join
                GUILD_BATTLE::CNormalGuildBattle::AskJoin(v7, na, dwGuildSeriala, dwCharacSeriala);
            }
            // Check if battle is currently in progress
            else if (GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v7)) {
                // Battle is active, handle login during battle
                GUILD_BATTLE::CNormalGuildBattle::LogIn(v7, na, dwGuildSeriala, dwCharacSeriala);
            }
        }
    }
}
