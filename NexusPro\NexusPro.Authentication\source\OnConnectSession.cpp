/*
 * OnConnectSession.cpp
 * Original Function: ?OnConnectSession@CHackShieldExSystem@@XH@Z
 * Original Address: 0x1404170D0
 * 
 * Description: Handles session connection events for the HackShield Ex System.
 * This function is called when a new session connects and initializes
 * the appropriate HackShield parameters for the session.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Handles session connection for HackShield Ex System
 * @param this Pointer to the CHackShieldExSystem instance
 * @param n Session identifier/number
 */
void CHackShieldExSystem::OnConnectSession(CHackShieldExSystem *this, int n) {
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-38h]@1
    BASE_HACKSHEILD_PARAM *v5; // [sp+20h] [bp-18h]@4
    CHackShieldExSystem *v6; // [sp+40h] [bp+8h]@1
    int na; // [sp+48h] [bp+10h]@1

    na = n;
    v6 = this;
    v2 = &v4;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    // Get HackShield parameters for this session
    v5 = CHackShieldExSystem::GetParam(v6, n);
    
    // If parameters exist, call the OnConnect callback
    if (v5) {
        ((void (*)(BASE_HACKSHEILD_PARAM *, QWORD))v5->vfptr->OnConnect)(v5, (unsigned int)na);
    }
}
