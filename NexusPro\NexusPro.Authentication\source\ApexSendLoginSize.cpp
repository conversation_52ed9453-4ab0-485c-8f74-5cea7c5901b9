/*
 * ApexSendLoginSize.cpp
 * Original Function: _apex_send_login::size
 * Original Address: 0x140410BF0
 * 
 * Description: Returns the size of the apex send login structure.
 * This function provides the fixed size (13 bytes) for apex login data packets.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Get size of apex send login structure
signed __int64 _apex_send_login::size(_apex_send_login *this) {
    return 13LL;  // Fixed size of 13 bytes for apex login packet
}
