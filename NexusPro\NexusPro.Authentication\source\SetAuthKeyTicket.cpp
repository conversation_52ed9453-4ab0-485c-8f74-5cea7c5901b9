/**
 * @file SetAuthKeyTicket.cpp
 * @brief RF Online Mining Ticket Authentication Key Set Function
 * @note Original Function: ?Set@_AuthKeyTicket@MiningTicket@@XGEEEE@Z
 * @note Original Address: 0x1400A6BA0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Sets the authentication key ticket with time parameters
 * @param this Pointer to MiningTicket::_AuthKeyTicket instance
 * @param byYear Year value (14 bits, bits 18-31)
 * @param byMonth Month value (4 bits, bits 14-17)
 * @param byDay Day value (5 bits, bits 9-13)
 * @param byHour Hour value (5 bits, bits 4-8)
 * @param byNumofTime Number of time units (4 bits, bits 0-3)
 * 
 * This function packs the time parameters into a single 32-bit value
 * using bit manipulation to store all time components efficiently.
 * The bit layout is:
 * - Bits 31-18: Year (14 bits)
 * - Bits 17-14: Month (4 bits)
 * - Bits 13-9:  Day (5 bits)
 * - Bits 8-4:   Hour (5 bits)
 * - Bits 3-0:   NumofTime (4 bits)
 */
void MiningTicket::_AuthKeyTicket::Set(MiningTicket::_AuthKeyTicket *this, unsigned __int16 byYear, char byMonth, char byDay, char byHour, char byNumofTime)
{
  // Set year (14 bits, shifted to position 18-31)
  this->uiData = ((byYear & 0x3FFF) << 18) | (this->uiData & 0x3FFFF);
  
  // Set month (4 bits, shifted to position 14-17)
  this->uiData = ((byMonth & 0xF) << 14) | (this->uiData & 0xFFFC3FFF);
  
  // Set day (5 bits, shifted to position 9-13)
  this->uiData = ((byDay & 0x1F) << 9) | (this->uiData & 0xFFFFC1FF);
  
  // Set hour (5 bits, shifted to position 4-8)
  this->uiData = (16 * (byHour & 0x1F)) | (this->uiData & 0xFFFFFE0F);
  
  // Set number of time units (4 bits, position 0-3)
  this->uiData = (byNumofTime & 0xF) | (this->uiData & 0xFFFFFFF0);
}
