/**
 * @file GetAsyncLogDirPath.cpp
 * @brief RF Online Async Log Info Get Directory Path Function
 * @note Original Function: ?GetDirPath@CAsyncLogInfo@@PEBDXZ
 * @note Original Address: 0x1403C1630
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Gets the directory path for the async log
 * @param this Pointer to CAsyncLogInfo instance
 * @return char* Pointer to the log directory path string
 * 
 * This function returns the directory path where log files are stored.
 * The path is set during initialization and used for log file operations.
 */
char *CAsyncLogInfo::GetDirPath(CAsyncLogInfo *this) {
  return this->m_szLogDirPath;
}
