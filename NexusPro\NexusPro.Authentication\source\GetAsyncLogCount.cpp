/**
 * @file GetAsyncLogCount.cpp
 * @brief RF Online Async Log Info Get Count Function
 * @note Original Function: CAsyncLogInfo::GetCount
 * @note Original Address: 0x1403C16B0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Gets the log type from the async log info
 * @return DWORD The current log type value
 * 
 * This function returns the log type stored in the async log info instance.
 * Note: The function name suggests it should return a count, but the implementation
 * returns the log type. This preserves the original decompiled behavior.
 */
DWORD CAsyncLogInfo::GetLogType() const {
  return m_dwLogType;
}
