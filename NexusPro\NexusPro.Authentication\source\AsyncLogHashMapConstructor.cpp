/*
 * AsyncLogHashMapConstructor.cpp
 * Original Function: hash_map constructor for AsyncLogInfo
 * Original Address: 0x1403C17E0
 * 
 * Description: Constructor for hash map container that stores AsyncLogInfo pointers.
 * Initializes the hash map with proper allocators and comparison functions.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <map>
#include <memory>
#include <unordered_map>
#include <utility>

// Constructor for AsyncLogInfo hash map
void stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::hash_map(
    stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *this) {
    
    __int64 *v1;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    std::allocator<std::pair<int const, CAsyncLogInfo *>> *v3;  // Allocator pointer
    stdext::hash_compare<int, std::less<int>> *v4;              // Hash compare pointer
    __int64 v5;                       // Stack buffer [sp+0h] [bp-38h]
    char v6;                          // Allocator storage [sp+20h] [bp-18h]
    char v7;                          // Hash compare storage [sp+21h] [bp-17h]
    std::allocator<std::pair<int const, CAsyncLogInfo *>> *_Al; // Allocator pointer [sp+28h] [bp-10h]
    stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *v9; // This pointer [sp+40h] [bp+8h]

    v9 = this;
    v1 = &v5;
    
    // Initialize stack buffer with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD *)v1 = 0xCCCCCCCC;   // Debug fill pattern
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Initialize allocator
    v3 = std::allocator<std::pair<int const, CAsyncLogInfo *>>::allocator((std::allocator<std::pair<int const, CAsyncLogInfo *>> *)&v6);
    _Al = v3;
    
    // Initialize hash compare function
    v4 = stdext::hash_compare<int, std::less<int>>::hash_compare((stdext::hash_compare<int, std::less<int>> *)&v7);
    
    // Initialize the underlying hash container
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>>::_Hash(
        (stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>> *)&v9->_Myfirstiter,
        v4,
        _Al);
}
