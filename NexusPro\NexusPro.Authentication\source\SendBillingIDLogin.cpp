/*
 * SendBillingIDLogin.cpp
 * Original Function: CBillingID::SendMsg_Login
 * Original Address: 0x14028E600
 * 
 * Description: Sends login message for billing ID authentication.
 * This function handles user login requests with billing system integration.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;

// Send login message for billing ID
char CBillingID::SendMsg_Login(CBillingID *this, char *szID, char *szIP, char *szCMS, __int16 iType, _SYSTEMTIME *pstEndDate, int lRemainTime) {
    __int64 *v7;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    char result;                       // Return value
    __int64 v10;                      // Stack buffer [sp+0h] [bp-C8h]
    char Dst;                         // ID buffer [sp+38h] [bp-90h]
    char v12;                         // IP buffer [sp+45h] [bp-83h]
    char v13;                         // CMS buffer [sp+55h] [bp-73h]
    __int16 v14;                      // Type value [sp+5Ch] [bp-6Ch]
    int v15;                          // Remain time [sp+5Eh] [bp-6Ah]
    char v16;                         // End date buffer [sp+62h] [bp-66h]
    char pbyType;                     // Message type [sp+94h] [bp-34h]
    char v18;                         // Message subtype [sp+95h] [bp-33h]
    unsigned __int64 v19;             // Security cookie [sp+B0h] [bp-18h]
    CBillingID *v20;                  // This pointer [sp+D0h] [bp+8h]
    char *v21;                        // IP pointer [sp+E0h] [bp+18h]
    char *v22;                        // CMS pointer [sp+E8h] [bp+20h]

    v22 = szCMS;
    v21 = szIP;
    v20 = this;
    v7 = &v10;
    
    // Initialize stack buffer with debug pattern
    for (i = 48LL; i; --i) {
        *(DWORD *)v7 = 0xCCCCCCCC;   // Debug fill pattern
        v7 = (__int64 *)((char *)v7 + 4);
    }
    
    // Set up stack security cookie
    v19 = (unsigned __int64)&v10 ^ _security_cookie;
    
    // Check if billing system is operational
    if (v20->m_bOper) {
        // Check if type is within valid range
        if (iType <= 100) {
            v14 = iType;
            v15 = lRemainTime;
            
            // Copy user ID (13 bytes)
            memcpy(&Dst, szID, 0xD);
            
            // Copy IP address (16 bytes)
            memcpy(&v12, v21, 0x10);
            
            // Copy CMS data if provided (7 bytes)
            if (v22) {
                memcpy(&v13, v22, 7);
            }
            
            // Copy end date if provided (16 bytes)
            if (pstEndDate) {
                memcpy(&v16, pstEndDate, 0x10);
            }
            
            // Set message type and subtype
            pbyType = 29;  // Billing login message type
            v18 = 4;       // Login subtype
            
            // Send the login message
            CNetProcess::LoadSendMsg(qword_1414F20A0, 0, &pbyType, &Dst, 0x3A);
            result = 1;    // Success
        } else {
            result = 0;    // Invalid type
        }
    } else {
        result = 0;        // Billing system not operational
    }
    
    return result;
}
