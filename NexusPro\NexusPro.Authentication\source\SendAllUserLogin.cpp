/*
 * SendAllUserLogin.cpp
 * Original Function: CBilling::SendMsg_CurAllUserLogin
 * Original Address: 0x14028D610
 * 
 * Description: Sends login messages for all currently active users to the billing system.
 * This function iterates through all user database entries and sends login notifications.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Send login messages for all current active users
void CBilling::SendMsg_CurAllUserLogin(CBilling *this) {
    __int64 *v1;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    char *v3;                         // IP address string
    __int64 v4;                       // Stack buffer [sp+0h] [bp-78h]
    __int16 v5;                       // Billing type [sp+20h] [bp-58h]
    _SYSTEMTIME *v6;                  // End date pointer [sp+28h] [bp-50h]
    int v7;                           // Remain time [sp+30h] [bp-48h]
    unsigned int j;                   // User loop counter [sp+40h] [bp-38h]
    CUserDB *v9;                      // Current user pointer [sp+48h] [bp-30h]
    _SYSTEMTIME *v10;                 // End date [sp+50h] [bp-28h]
    char *v11;                        // CMS string [sp+58h] [bp-20h]
    CBillingVtbl *v12;                // Virtual function table [sp+60h] [bp-18h]
    CBilling *v13;                    // This pointer [sp+80h] [bp+8h]

    v13 = this;
    v1 = &v4;
    
    // Initialize stack buffer with debug pattern
    for (i = 28LL; i; --i) {
        *(DWORD *)v1 = 0xCCCCCCCC;   // Debug fill pattern
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Check if billing system is operational
    if (v13->m_bOper) {
        // Iterate through all user database entries (0x9E4 = 2532 users)
        for (j = 0; j < 0x9E4; ++j) {
            v9 = &g_UserDB[j];
            
            // Check if user is active
            if (v9->m_bActive) {
                // Check if user is not degraded (normal user)
                if (!v9->m_byUserDgr) {
                    // Prepare billing information
                    v10 = &v9->m_BillingInfo.stEndDate;
                    v11 = v9->m_BillingInfo.szCMS;
                    
                    // Convert IP address to string
                    v3 = inet_ntoa((struct in_addr)v9->m_dwIP);
                    
                    // Get virtual function table
                    v12 = v13->vfptr;
                    
                    // Set up billing parameters
                    v7 = v9->m_BillingInfo.lRemainTime;
                    v6 = v10;
                    v5 = v9->m_BillingInfo.iType;
                    
                    // Call virtual SendMsg_Login function
                    ((void (*)(CBilling *, __int64, char *, __int64))v12->SendMsg_Login)(
                        v13,
                        (__int64)v9->m_szAccountID,  // Account ID
                        v3,                          // IP address string
                        (__int64)v11);               // CMS string
                }
            }
        }
    }
}
