/*
 * HMACDestructor.cpp
 * Original Function: MessageAuthenticationCodeImpl HMAC Destructor
 * Original Address: 0x140464F50
 * 
 * Description: Destructor for HMAC-SHA1 message authentication code implementation.
 * Cleans up the CryptoPP HMAC authentication system and releases resources.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Destructor for HMAC-SHA1 message authentication code implementation
void CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>::~MessageAuthenticationCodeImpl(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>> *this) {
    
    __int64 *v1;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v3;                       // Stack buffer [sp+0h] [bp-28h]
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>> *v4; // This pointer [sp+30h] [bp+8h]

    v4 = this;
    v1 = &v3;
    
    // Initialize stack buffer with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD *)v1 = 0xCCCCCCCC;   // Debug fill pattern
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Call base class destructor to clean up algorithm implementation
    CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>, CryptoPP::HMAC<CryptoPP::SHA1>>::~AlgorithmImpl(
        (CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>, CryptoPP::HMAC<CryptoPP::SHA1>> *)&v4->vfptr);
}
