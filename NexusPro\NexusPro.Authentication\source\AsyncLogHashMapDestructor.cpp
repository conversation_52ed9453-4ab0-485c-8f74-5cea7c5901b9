/*
 * AsyncLogHashMapDestructor.cpp
 * Original Function: hash_map destructor for AsyncLogInfo
 * Original Address: 0x1403C1170
 * 
 * Description: Destructor for hash map container that stores AsyncLogInfo pointers.
 * Cleans up the hash map and releases all allocated resources.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <map>
#include <memory>
#include <unordered_map>
#include <utility>

// Destructor for AsyncLogInfo hash map
void stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>>::~hash_map(
    stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *this) {
    
    __int64 *v1;                       // Stack pointer for initialization
    signed __int64 i;                  // Loop counter for stack initialization
    __int64 v3;                       // Stack buffer [sp+0h] [bp-28h]
    stdext::hash_map<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>> *v4; // This pointer [sp+30h] [bp+8h]

    v4 = this;
    v1 = &v3;
    
    // Initialize stack buffer with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD *)v1 = 0xCCCCCCCC;   // Debug fill pattern
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Call base hash container destructor to clean up resources
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo *, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo *>>, 0>>::~_Hash(
        &v4->_Myfirstiter);
}
