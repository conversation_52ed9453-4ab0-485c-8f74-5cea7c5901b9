/*
 * IncreaseAsyncLogCount.cpp
 * Original Function: ?IncreaseCount@CAsyncLogInfo@@XXZ
 * Original Address: 0x1403C16F0
 * 
 * Description: Increments the log count for the CAsyncLogInfo object.
 * This function increases the internal log ID counter which serves as a count mechanism.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * Increases the log count by incrementing the log ID
 * This function is used to track the number of log entries processed
 */
void CAsyncLogInfo::IncreaseCount() {
    ++m_dwLogID; // Using m_dwLogID as our count member
}
