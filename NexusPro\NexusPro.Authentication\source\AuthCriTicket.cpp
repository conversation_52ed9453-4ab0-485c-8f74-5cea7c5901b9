/**
 * @file AuthCriTicket.cpp
 * @brief RF Online Mining Ticket Critical Authentication Function
 * @note Original Function: ?AuthLastCriTicket@MiningTicket@@HGEEEE@Z
 * @note Original Address: 0x1400D01D0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Authenticates the last critical ticket for mining operations
 * @param this Pointer to MiningTicket instance
 * @param byCurrentYear Current year value
 * @param byCurrentMonth Current month value
 * @param byCurrentDay Current day value
 * @param byCurrentHour Current hour value
 * @param byNumOfTime Number of time units
 * @return __int64 Authentication result (0 for failure, non-zero for success)
 * 
 * This function validates the last critical mining ticket by comparing
 * the current time parameters with the stored ticket data.
 */
__int64 MiningTicket::AuthLastCriTicket(MiningTicket *this, unsigned __int16 byCurrentYear, char byCurrentMonth, char byCurrentDay, char byCurrentHour, char byNumOfTime) {
  __int64*v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v9; // [sp+0h] [bp-58h]@1
  MiningTicket::_AuthKeyTicket v10; // [sp+34h] [bp-24h]@6
  MiningTicket*v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v6 = &v9;
  
  // Initialize memory buffer with debug pattern (0xCCCCCCCC)
  for(i = 20LL; i; --i) {
    *(DWORD *)v6 = 0xCCCCCCCC;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  
  // Check if the last critical ticket data exists
  if(v11->m_dwTakeLastCriTicket.uiData) {
    // Set up authentication key ticket with current time parameters
    MiningTicket::_AuthKeyTicket::Set(&v10, byCurrentYear, byCurrentMonth, byCurrentDay, byCurrentHour, byNumOfTime);
    
    // Compare the new ticket with the stored last critical ticket
    // Returns 0 if they are equal (authentication successful)
    result = MiningTicket::_AuthKeyTicket::operator!=(&v10, &v11->m_dwTakeLastCriTicket) == 0;
  }
  else
  {
    // No ticket data available, authentication fails
    result = 0LL;
  }
  return result;
}
