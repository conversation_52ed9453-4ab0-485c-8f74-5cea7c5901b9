/*
 * SetMiningTicketAuth.cpp
 * Original Function: MiningTicket::_AuthKeyTicket::Set
 * Original Address: 0x1400A6BA0
 * 
 * Description: Sets the authentication key ticket data for mining operations.
 * Packs date/time information and usage count into a 32-bit data field using bit manipulation.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Set mining ticket authentication key data
void MiningTicket::_AuthKeyTicket::Set(MiningTicket::_AuthKeyTicket *this, unsigned __int16 byYear, char by<PERSON><PERSON>h, char byDay, char byHour, char byNumofTime) {
    // Pack year (14 bits, bits 18-31) - supports years 0-16383
    this->uiData = ((byYear & 0x3FFF) << 18) | (this->uiData & 0x3FFFF);
    
    // Pack month (4 bits, bits 14-17) - supports months 0-15
    this->uiData = ((byMonth & 0xF) << 14) | (this->uiData & 0xFFFC3FFF);
    
    // Pack day (5 bits, bits 9-13) - supports days 0-31
    this->uiData = ((byDay & 0x1F) << 9) | (this->uiData & 0xFFFFC1FF);
    
    // Pack hour (5 bits, bits 4-8) - supports hours 0-31
    this->uiData = (16 * (byHour & 0x1F)) | (this->uiData & 0xFFFFFE0F);
    
    // Pack number of times (4 bits, bits 0-3) - supports counts 0-15
    this->uiData = (byNumofTime & 0xF) | (this->uiData & 0xFFFFFFF0);
}
